/**
 * 抖音内部底稿数据服务
 *
 * 🎯 核心功能：
 * 1. 封装抖音内部底稿数据接口调用
 * 2. 实现智能缓存机制提升查询性能
 * 3. 提供完善的错误处理和分类
 * 4. 支持请求超时控制和并发管理
 * 5. 数据格式化和验证
 *
 * 🔗 底稿数据接口：
 * - URL: https://9gzj7t9k.fn.bytedance.net/api/search/stream (已修复HTTPS协议)
 * - 参数: query (查询关键词), summary_only (是否只返回摘要)
 * - 返回: { answer, pv, reasoning, reference } 结构的JSON数据
 *
 * 🚨 重要修复：
 * - 原HTTP协议导致混合内容错误 (blocked:mixed-content)
 * - 已升级为HTTPS协议以解决安全限制
 *
 * 💡 使用场景：
 * - 当用户开启"使用抖音内部底稿数据"模式时
 * - 系统需要获取权威、准确的底稿数据作为AI生成的数据源
 * - 确保生成内容的准确性和权威性
 *
 * 🚀 性能特性：
 * - 5分钟智能缓存，避免重复请求
 * - 30秒请求超时，确保用户体验
 * - 自动清理过期缓存，防止内存泄漏
 * - 支持缓存统计和性能监控
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @version 1.0.0
 */

import createLogger from '../../code_generate/utils/logger';

const logger = createLogger('InternalDataService');

/**
 * 底稿数据响应接口
 *
 * 📋 数据结构说明：
 * - success: 请求是否成功
 * - data: 底稿数据内容，包含 answer、pv、reasoning、reference 字段
 * - summary: 从底稿数据中提取的摘要信息
 * - error: 错误信息（仅在失败时存在）
 * - source: 数据来源标识（'cache' 表示缓存，'api' 表示接口调用）
 * - timestamp: 数据获取时间戳，用于缓存管理
 *
 * 💡 使用示例：
 * ```typescript
 * const response = await InternalDataService.fetchInternalData('九九乘法表');
 * if (response.success) {
 *   console.log('答案内容:', response.data.answer);
 *   console.log('数据热度:', response.data.pv);
 *   console.log('数据摘要:', response.summary);
 * }
 * ```
 */
export interface InternalDataResponse {
  /** 请求是否成功 */
  success: boolean;
  /** 底稿数据内容，成功时包含 answer、pv、reasoning、reference 等字段 */
  data: any;
  /** 从底稿数据中提取的摘要信息，便于快速预览 */
  summary?: string;
  /** 错误信息，仅在请求失败时存在 */
  error?: string;
  /** 数据来源：'cache' 表示来自缓存，'api' 表示来自接口调用 */
  source: 'cache' | 'api';
  /** 数据获取的时间戳，用于缓存有效期判断 */
  timestamp: number;
}

/**
 * 底稿数据错误类型枚举
 *
 * 🚨 错误分类说明：
 * - NETWORK_ERROR: 网络连接失败，通常是网络不可达或DNS解析失败
 * - TIMEOUT_ERROR: 请求超时，接口响应时间超过30秒限制
 * - PARSE_ERROR: 数据解析失败，返回的不是有效的JSON格式
 * - API_ERROR: 接口调用失败，返回非200状态码
 * - INVALID_QUERY: 查询参数无效，如空字符串或格式错误
 *
 * 💡 错误处理策略：
 * - NETWORK_ERROR/TIMEOUT_ERROR: 提供重试选项，可能是临时网络问题
 * - PARSE_ERROR/API_ERROR: 自动回退到直接AI模式，避免影响用户体验
 * - INVALID_QUERY: 提示用户修正输入内容
 */
export enum InternalDataError {
  /** 网络连接失败 - 无法连接到底稿数据服务器 */
  NETWORK_ERROR = 'NETWORK_ERROR',
  /** 请求超时 - 接口响应时间超过设定的超时限制 */
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  /** 数据解析失败 - 返回的数据不是有效的JSON格式 */
  PARSE_ERROR = 'PARSE_ERROR',
  /** 接口调用失败 - 服务器返回错误状态码 */
  API_ERROR = 'API_ERROR',
  /** 查询参数无效 - 输入的查询内容为空或格式不正确 */
  INVALID_QUERY = 'INVALID_QUERY',
}

/**
 * 缓存项接口
 *
 * 📦 缓存机制说明：
 * - data: 缓存的底稿数据响应内容
 * - expireTime: 缓存过期时间戳，用于判断缓存是否有效
 *
 * 🕒 缓存策略：
 * - 缓存时长：5分钟，平衡性能和数据新鲜度
 * - 缓存键：基于查询内容和summary_only参数生成
 * - 自动清理：定期清理过期缓存，防止内存泄漏
 */
interface CacheItem {
  /** 缓存的底稿数据响应内容 */
  data: InternalDataResponse;
  /** 缓存过期时间戳（毫秒），超过此时间缓存将被清理 */
  expireTime: number;
}

/**
 * 抖音内部底稿数据服务类
 *
 * 🏗️ 架构设计：
 * - 单例模式：所有方法都是静态方法，全局共享缓存和配置
 * - 缓存优先：优先使用缓存数据，减少网络请求
 * - 错误容错：完善的错误处理，确保系统稳定性
 * - 性能监控：提供缓存统计和性能指标
 *
 * 🔧 配置参数：
 * - BASE_URL: 底稿数据接口地址
 * - CACHE_DURATION: 缓存有效期（5分钟）
 * - REQUEST_TIMEOUT: 请求超时时间（30秒）
 *
 * 💾 缓存管理：
 * - 使用Map结构存储缓存数据
 * - 基于查询内容和参数生成缓存键
 * - 自动清理过期缓存，防止内存泄漏
 */
export class InternalDataService {
  /**
   * 🚨 【重要修复】底稿数据接口的基础URL地址
   *
   * 修复说明：
   * - 原HTTP协议在HTTPS页面中被浏览器阻止 (blocked:mixed-content)
   * - 已升级为HTTPS协议以解决混合内容安全限制
   * - 确保在HTTPS环境下能正常访问底稿数据接口
   */
  private static readonly BASE_URL =
    'https://9gzj7t9k.fn.bytedance.net/api/search/stream';
  /** 缓存有效期：5分钟（毫秒） */
  private static readonly CACHE_DURATION = 5 * 60 * 1000;
  /** 请求超时时间：30秒（毫秒） */
  private static readonly REQUEST_TIMEOUT = 30000;
  /** 缓存存储：使用Map结构存储查询结果 */
  private static cache = new Map<string, CacheItem>();

  /**
   * 获取底稿数据
   * @param query 查询关键词
   * @param summaryOnly 是否只返回摘要
   * @returns 底稿数据响应
   */
  static async fetchInternalData(
    query: string,
    summaryOnly: boolean = true,
  ): Promise<InternalDataResponse> {
    // 参数验证
    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      logger.error('[fetchInternalData] 无效的查询参数:', query);
      return this.createErrorResponse(
        InternalDataError.INVALID_QUERY,
        '查询参数不能为空',
      );
    }

    const trimmedQuery = query.trim();
    const cacheKey = `${trimmedQuery}_${summaryOnly}`;

    // 检查缓存
    const cachedResult = this.getCachedData(cacheKey);
    if (cachedResult) {
      logger.info('[fetchInternalData] 使用缓存数据:', {
        query: trimmedQuery,
        cacheKey,
      });
      return cachedResult;
    }

    // 记录请求开始
    const startTime = Date.now();
    logger.info('[fetchInternalData] 开始请求底稿数据:', {
      query: trimmedQuery,
      summaryOnly,
      timestamp: new Date().toISOString(),
    });

    try {
      // 构建请求URL
      const url = this.buildRequestUrl(trimmedQuery, summaryOnly);

      // 创建超时控制器
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
        logger.warn('[fetchInternalData] 请求超时:', {
          query: trimmedQuery,
          timeout: this.REQUEST_TIMEOUT,
        });
      }, this.REQUEST_TIMEOUT);

      // 发送请求
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        signal: controller.signal,
        // 添加请求配置
        mode: 'cors',
        cache: 'no-cache',
        credentials: 'same-origin',
      });

      // 清除超时定时器
      clearTimeout(timeoutId);

      // 检查响应状态
      if (!response.ok) {
        const errorText = await response.text().catch(() => '无法获取错误详情');
        logger.error('[fetchInternalData] API请求失败:', {
          query: trimmedQuery,
          status: response.status,
          statusText: response.statusText,
          errorText: errorText.substring(0, 200),
        });

        return this.createErrorResponse(
          InternalDataError.API_ERROR,
          `接口请求失败: ${response.status} ${response.statusText}`,
        );
      }

      // 解析响应数据
      const responseData = await response.json();
      const duration = Date.now() - startTime;

      logger.info('[fetchInternalData] 底稿数据获取成功:', {
        query: trimmedQuery,
        duration,
        dataSize: JSON.stringify(responseData).length,
      });

      // 构建成功响应
      const result: InternalDataResponse = {
        success: true,
        data: responseData,
        summary: responseData.summary || this.extractSummary(responseData),
        source: 'api',
        timestamp: Date.now(),
      };

      // 缓存结果
      this.setCachedData(cacheKey, result);

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;

      // 处理不同类型的错误
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          logger.error('[fetchInternalData] 请求超时:', {
            query: trimmedQuery,
            duration,
          });
          return this.createErrorResponse(
            InternalDataError.TIMEOUT_ERROR,
            '请求超时，请稍后重试',
          );
        }

        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          logger.error('[fetchInternalData] 网络错误:', {
            query: trimmedQuery,
            error: error.message,
          });
          return this.createErrorResponse(
            InternalDataError.NETWORK_ERROR,
            '网络连接失败，请检查网络设置',
          );
        }

        if (error.name === 'SyntaxError') {
          logger.error('[fetchInternalData] 数据解析错误:', {
            query: trimmedQuery,
            error: error.message,
          });
          return this.createErrorResponse(
            InternalDataError.PARSE_ERROR,
            '数据格式错误，无法解析响应',
          );
        }
      }

      logger.error('[fetchInternalData] 未知错误:', {
        query: trimmedQuery,
        duration,
        error: error instanceof Error ? error.message : String(error),
      });

      return this.createErrorResponse(
        InternalDataError.NETWORK_ERROR,
        error instanceof Error ? error.message : '未知错误',
      );
    }
  }

  /**
   * 构建请求URL
   * @param query 查询关键词
   * @param summaryOnly 是否只返回摘要
   * @returns 完整的请求URL
   */
  private static buildRequestUrl(query: string, summaryOnly: boolean): string {
    const url = new URL(this.BASE_URL);
    url.searchParams.set('query', query);
    url.searchParams.set('summary_only', summaryOnly ? '1' : '0');

    logger.debug('[buildRequestUrl] 构建请求URL:', {
      query,
      summaryOnly,
      url: url.toString(),
    });

    return url.toString();
  }

  /**
   * 获取缓存数据
   * @param cacheKey 缓存键
   * @returns 缓存的数据或null
   */
  private static getCachedData(cacheKey: string): InternalDataResponse | null {
    const cacheItem = this.cache.get(cacheKey);

    if (!cacheItem) {
      return null;
    }

    // 检查是否过期
    if (Date.now() > cacheItem.expireTime) {
      this.cache.delete(cacheKey);
      logger.debug('[getCachedData] 缓存已过期，删除缓存项:', { cacheKey });
      return null;
    }

    // 更新source标记为cache
    const cachedData = { ...cacheItem.data, source: 'cache' as const };
    logger.debug('[getCachedData] 命中缓存:', {
      cacheKey,
      timestamp: cacheItem.data.timestamp,
    });

    return cachedData;
  }

  /**
   * 设置缓存数据
   * @param cacheKey 缓存键
   * @param data 要缓存的数据
   */
  private static setCachedData(
    cacheKey: string,
    data: InternalDataResponse,
  ): void {
    const expireTime = Date.now() + this.CACHE_DURATION;
    this.cache.set(cacheKey, { data, expireTime });

    logger.debug('[setCachedData] 数据已缓存:', {
      cacheKey,
      expireTime: new Date(expireTime).toISOString(),
      cacheSize: this.cache.size,
    });

    // 清理过期缓存（防止内存泄漏）
    this.cleanExpiredCache();
  }

  /**
   * 清理过期缓存
   */
  private static cleanExpiredCache(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expireTime) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.debug('[cleanExpiredCache] 清理过期缓存:', {
        cleanedCount,
        remainingSize: this.cache.size,
      });
    }
  }

  /**
   * 创建错误响应
   * @param errorType 错误类型
   * @param message 错误消息
   * @returns 错误响应对象
   */
  private static createErrorResponse(
    errorType: InternalDataError,
    message: string,
  ): InternalDataResponse {
    return {
      success: false,
      data: null,
      error: message,
      source: 'api',
      timestamp: Date.now(),
    };
  }

  /**
   * 从响应数据中提取摘要
   * @param data 响应数据
   * @returns 提取的摘要
   */
  private static extractSummary(data: any): string {
    if (!data) return '暂无摘要信息';

    // 优先从answer字段提取摘要（抖音底稿数据的核心字段）
    if (typeof data.answer === 'string' && data.answer.trim()) {
      // 移除HTML标记，提取纯文本作为摘要
      const cleanAnswer = data.answer
        .replace(/<[^>]*>/g, '') // 移除HTML标签
        .replace(/🔶\d+🔷/g, '') // 移除特殊标记
        .trim();

      // 如果内容过长，截取前200个字符
      if (cleanAnswer.length > 200) {
        return cleanAnswer.substring(0, 200) + '...';
      }
      return cleanAnswer;
    }

    // 备选：从reference字段提取摘要
    if (typeof data.reference === 'string' && data.reference.trim()) {
      const referenceLines = data.reference
        .split('\n')
        .filter(line => line.trim());
      if (referenceLines.length > 0) {
        // 提取第一个有意义的行作为摘要
        for (const line of referenceLines) {
          if (
            line.length > 20 &&
            !line.startsWith('今天日期：') &&
            !line.startsWith('请结合')
          ) {
            return line.length > 200 ? line.substring(0, 200) + '...' : line;
          }
        }
      }
    }

    // 备选：从reasoning字段提取
    if (typeof data.reasoning === 'string' && data.reasoning.trim()) {
      const reasoning = data.reasoning.trim();
      return reasoning.length > 200
        ? reasoning.substring(0, 200) + '...'
        : reasoning;
    }

    // 最后备选：显示数据热度信息
    if (typeof data.pv === 'number' && data.pv > 0) {
      return `热门内容，已有 ${data.pv} 次浏览`;
    }

    return '暂无摘要信息';
  }

  /**
   * 清空所有缓存
   */
  static clearCache(): void {
    const cacheSize = this.cache.size;
    this.cache.clear();
    logger.info('[clearCache] 已清空所有缓存:', { previousSize: cacheSize });
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计
   */
  static getCacheStats(): {
    size: number;
    keys: string[];
    totalMemoryUsage: number;
  } {
    const keys = Array.from(this.cache.keys());
    let totalMemoryUsage = 0;

    // 估算内存使用量
    for (const [key, item] of this.cache.entries()) {
      totalMemoryUsage += key.length * 2; // 字符串按2字节计算
      totalMemoryUsage += JSON.stringify(item.data).length * 2;
    }

    return {
      size: this.cache.size,
      keys,
      totalMemoryUsage,
    };
  }

  /**
   * 预热缓存（批量预加载常用查询）
   * @param queries 要预加载的查询列表
   */
  static async warmupCache(queries: string[]): Promise<void> {
    logger.info('[warmupCache] 开始预热缓存:', { queryCount: queries.length });

    const promises = queries.map(query =>
      this.fetchInternalData(query, true).catch(error => {
        logger.warn('[warmupCache] 预热失败:', { query, error: error.message });
        return null;
      }),
    );

    const results = await Promise.allSettled(promises);
    const successCount = results.filter(
      result => result.status === 'fulfilled',
    ).length;

    logger.info('[warmupCache] 缓存预热完成:', {
      total: queries.length,
      success: successCount,
      cacheSize: this.cache.size,
    });
  }
}
