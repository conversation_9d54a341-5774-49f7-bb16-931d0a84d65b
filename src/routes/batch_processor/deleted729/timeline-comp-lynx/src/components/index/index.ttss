/* Timeline 组件样式 */

/* 容器样式 */
.timeline-container {
  height: 100%;
  width: 100%;
  padding: 40rpx;
  background-color: #f8f9fa;
}

/* 标题区域 */
.timeline-header {
  margin-bottom: 60rpx;
  text-align: center;
}

.timeline-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.2;
  text-align: center;
}

/* 内容区域 */
.timeline-content {
  position: relative;
  padding-left: 40rpx;
}

/* 空状态 */
.timeline-empty {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #95a5a6;
  font-size: 28rpx;
}

/* 时间线项目 */
.timeline-item {
  position: relative;
  margin-bottom: 60rpx;
  display: flex;
  align-items: flex-start;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.3s ease;
}

.timeline-item-animated {
  opacity: 1;
  transform: translateY(0);
}

/* 日期区域 */
.timeline-date-section {
  position: relative;
  margin-right: 40rpx;
  min-width: 200rpx;
}

.timeline-date {
  font-size: 28rpx;
  color: #7f8c8d;
  font-weight: 500;
  line-height: 1.4;
}

.timeline-dot {
  position: absolute;
  right: -20rpx;
  top: 8rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #3498db;
  border: 4rpx solid #ffffff;
}

/* 连接线 */
.timeline-line {
  position: absolute;
  left: 219rpx;
  top: 40rpx;
  width: 2rpx;
  height: 80rpx;
  background-color: #bdc3c7;
}

/* 内容区域 */
.timeline-content-section {
  flex: 1;
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 16rpx;
  border: 1rpx solid #e8e8e8;
  margin-left: 20rpx;
}

.timeline-event-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  line-height: 1.3;
}

.timeline-description {
  font-size: 26rpx;
  color: #5a6c7d;
  line-height: 1.5;
  letter-spacing: 0.5rpx;
  overflow: hidden;
  max-height: 80rpx;
}

/* 媒体内容样式 */
.media-scroller {
  display: flex;
  gap: 16rpx;
  overflow-x: auto;
  margin-top: 24rpx;
}

.media-item {
  position: relative;
  width: 176rpx;
  height: 116rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
  overflow: hidden;
}

.media-thumbnail,
.video-thumbnail {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 48rpx;
  height: 48rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.play-icon {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 12rpx 0 12rpx 20rpx;
  border-color: transparent transparent transparent white;
  margin-left: 6rpx;
}

/* 小屏幕适配 */
.timeline-container-small {
  padding: 20rpx;
}

.timeline-title-small {
  font-size: 42rpx;
}

.timeline-date-section-small {
  min-width: 160rpx;
}

.timeline-content-section-small {
  padding: 20rpx;
}

.timeline-event-title-small {
  font-size: 28rpx;
}

.timeline-description-small {
  font-size: 24rpx;
}
