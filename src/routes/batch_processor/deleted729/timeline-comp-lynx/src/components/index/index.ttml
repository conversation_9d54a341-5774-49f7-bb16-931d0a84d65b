<!-- Timeline 时间线组件模板 -->
<scroll-view class="timeline-container" scroll-y="true">
  <!-- 标题区域，支持通过插槽自定义 -->
  <!-- 使用方式：<view slot="header">自定义标题内容</view> -->
  <slot name="header">
    <view class="timeline-header">
      <text class="timeline-title">时间线</text>
    </view>
  </slot>

  <view class="timeline-content">
    <!-- 时间线为空时的提示 -->
    <!-- 使用方式：<view slot="empty">自定义空状态内容</view> -->
    <view class="timeline-empty" tt:if="{{!internalTimelineData || internalTimelineData.length === 0}}">
      <slot name="empty">
        <text>暂无数据</text>
      </slot>
    </view>

    <!-- 时间线项目列表 -->
    <block tt:for="{{internalTimelineData}}" tt:key="id">
      <view 
        class="timeline-item {{item.animated ? 'timeline-item-animated' : ''}}"
        style="margin-bottom: 50rpx;"
        bindtap="onTimelineItemTap" 
        data-index="{{index}}"
        aria-label="{{item.title}} - {{item.date}}">
        
        <!-- 日期部分 -->
        <view class="timeline-date-section">
          <text class="timeline-date">{{item.date}}</text>
          <view class="timeline-dot"></view>
        </view>

        <!-- 连接线，最后一项不显示 -->
        <view class="timeline-line" tt:if="{{!item.isLast}}"></view>

        <!-- 内容部分，支持通过插槽自定义 -->
        <!-- 使用方式：<view slot="item" data="{{item}}" index="{{index}}">自定义内容</view> -->
        <slot name="item" data="{{item}}" index="{{index}}">
          <view class="timeline-content-section">
            <text class="timeline-event-title">{{item.title}}</text>
            <text class="timeline-description">{{item.content || item.description}}</text>

            <!-- 媒体内容 -->
            <view class="media-scroller" tt:if="{{item.media && item.media.length > 0}}">
              <block tt:for="{{item.media}}" tt:key="*this">
                <view class="media-item" tt:if="{{item.type === 'image'}}">
                  <image src="{{item.thumbnail}}" class="media-thumbnail" mode="aspectFill" />
                </view>
                <view class="media-item" tt:if="{{item.type === 'video'}}">
                  <image src="{{item.thumbnail}}" class="video-thumbnail" mode="aspectFill" />
                  <view class="play-button">
                    <view class="play-icon"></view>
                  </view>
                </view>
              </block>
            </view>
          </view>
        </slot>
      </view>
    </block>
  </view>

  <!-- 底部区域，支持通过插槽自定义 -->
  <!-- 使用方式：<view slot="footer">自定义底部内容</view> -->
  <slot name="footer"></slot>
</scroll-view>
