/**
 * Timeline 组件类型定义
 */

/**
 * 媒体项目数据结构
 */
export interface MediaItem {
  /** 媒体类型，'image' 或 'video' */
  type: 'image' | 'video';
  /** 预览图 URL */
  thumbnail: string;
  /** 视频 URL，仅当 type 为 'video' 时有效 */
  videoSrc?: string;
}

/**
 * 时间线项目数据结构
 */
export interface TimelineItem {
  /** 事件日期 (必须) */
  date: string;
  /** 事件标题 (必须) */
  title: string;
  /** 事件描述，最多显示两行 (必须) */
  content: string;
  /** 媒体对象数组 (可选) */
  media?: MediaItem[];
  /** 是否已动画 (内部使用) */
  animated?: boolean;
  /** 是否为最后一项 (内部使用) */
  isLast?: boolean;
}

/**
 * 组件数据结构
 */
export interface Data {
  /** 内部时间线数据 */
  internalTimelineData: TimelineItem[];
  /** 是否正在执行动画 */
  isAnimating: boolean;
}

/**
 * 组件属性结构
 */
export interface Props {
  /** 时间线数据的JSON字符串格式 */
  list: string;
  /** 动画类型，支持 'fade', 'slide', 'bounce', 'scale', 'flip' */
  animation: 'fade' | 'slide' | 'bounce' | 'scale' | 'flip';
}

/**
 * 组件方法结构
 */
export interface Methods {
  /** 列表数据变化处理方法 */
  onListChange: (newVal: string) => void;
  /** 执行时间线动画 */
  animateTimeline: () => void;
  /** 执行单个项目动画 */
  animateTimelineItem: (index: number) => void;
  /** 项目点击处理方法 */
  onTimelineItemTap: (event: any) => void;
}

// 导出类型定义 (用于文档参考)
module.exports = {
  /**
   * 创建时间线项目数据
   * @param {string} date
   * @param {string} title
   * @param {string} content
   * @param {MediaItem[]} [media]
   * @returns {TimelineItem}
   */
  createTimelineItem: function (date, title, content, media) {
    return {
      date: date,
      title: title,
      content: content,
      media: media || [],
      animated: false,
      isLast: false,
    };
  },

  /**
   * 验证时间线数据格式
   * @param {TimelineItem[]} data
   * @returns {boolean}
   */
  validateTimelineData: function (data) {
    if (!Array.isArray(data)) {
      return false;
    }

    return data.every(function (item) {
      return (
        item &&
        typeof item.date === 'string' &&
        typeof item.title === 'string' &&
        typeof item.content === 'string'
      );
    });
  },
};
