import { showToast, showModal } from '@byted-lina/morphling-bridge';
import { LinaComponent } from '@byted-lina/runtime-ng';
import { Data, Props, Methods } from './types';

/**
 * Timeline 时间线组件
 * 参考 avatar 组件的定义方式，符合 lynx 规范
 */

LinaComponent<Data, Props, Methods>({
  data: {
    internalTimelineData: [],
    isAnimating: false,
  },

  properties: {
    list: {
      type: String,
      value: '[]',
      observer: 'onListChange',
    },
    animation: {
      type: String,
      value: 'fade',
    },
  },

  methods: {
    // 列表数据变化观察者
    onListChange: function (newVal) {
      if (!this.setData || !newVal) {
        return;
      }

      var parsedData;
      try {
        parsedData = JSON.parse(newVal);
      } catch (e) {
        console.error('Failed to parse list attribute:', e);
        parsedData = [];
      }

      var data = parsedData.map(function (item, index) {
        return Object.assign({}, item, {
          animated: false,
          isLast: index === parsedData.length - 1,
        });
      });

      this.setData({
        internalTimelineData: data,
      });

      if (this.properties.animation && data.length > 0) {
        this.animateTimeline();
      }
    },

    // 动画处理
    animateTimeline: function () {
      if (!this.data || !this.setData || this.data.isAnimating) {
        return;
      }

      this.setData({ isAnimating: true });
      this.animateTimelineItem(0);
    },

    animateTimelineItem: function (index) {
      var currentItems = this.data.internalTimelineData;
      if (!currentItems || index >= currentItems.length) {
        this.setData({ isAnimating: false });
        return;
      }

      setTimeout(
        function () {
          var newTimelineData = currentItems.map(
            function (dataItem, dataIndex) {
              if (dataIndex === index) {
                return Object.assign({}, dataItem, { animated: true });
              }
              return Object.assign({}, dataItem);
            },
          );

          this.setData({ internalTimelineData: newTimelineData });
          this.animateTimelineItem(index + 1);
        }.bind(this),
        200,
      );
    },

    // 时间线项目点击处理
    onTimelineItemTap: function (event) {
      var index = event.currentTarget.dataset.index;
      var item = this.data.internalTimelineData[index];

      if (item) {
        this.triggerEvent('itemtap', { item: item, index: index });

        // 使用导入的 showModal 方法
        showModal({
          title: item.title || '详情',
          content: item.description || item.content || '暂无详情',
          confirmText: '确定',
          success: function (res) {
            if (res.confirm) {
              console.log('用户点击确定');
            }
          },
        });
      }
    },
  },

  attached: function () {
    console.log('Timeline component attached');
  },

  ready: function () {
    console.log('Timeline component ready');
    if (this.properties && this.properties.list) {
      this.onListChange(this.properties.list);
    }
  },

  detached: function () {
    console.log('Timeline component detached');
  },
});
