// @ts-check
const { defineLinaConfig } = require('@byted-lina/cli');

/**
 * @type {import('@byted-lina/lina-config-douyin-search/dist/types').Config }
 */
const sceneConfig = {
  pluginOptions: [],
};

module.exports = defineLinaConfig({
  type: 'component',
  enableLynxNodiff: true,
  enableLynxNodiffPluginTailwind: true,
  useLatestSpeedy: true,
  bundleSuffix: 'nodiff',
  nodiffOptions: {
    enableLynxKey: true,
  },
  pageConfig: {
    enableReduceInitDataCopy: true,
    enableA11y: true,
  },
  ...sceneConfig,
});
