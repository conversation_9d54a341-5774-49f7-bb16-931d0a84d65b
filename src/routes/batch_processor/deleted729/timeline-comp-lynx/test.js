/**
 * Timeline 组件测试文件
 * 用于验证组件功能是否正常
 */

// 引入类型工具函数
var types = require('./src/types.js');

// 测试数据
var testTimelineData = [
  {
    date: "2024-01-01",
    title: "项目启动",
    content: "开始新项目开发，制定项目计划和技术方案"
  },
  {
    date: "2024-03-15",
    title: "原型完成",
    content: "完成产品原型设计，确定用户界面和交互流程",
    media: [
      {
        type: "image",
        thumbnail: "https://images.unsplash.com/photo-1618477388954-7852f32655ec?q=80&w=200&auto=format&fit=crop"
      }
    ]
  },
  {
    date: "2024-06-20",
    title: "测试发布",
    content: "发布测试版本，收集用户反馈和bug报告"
  },
  {
    date: "2024-08-30",
    title: "正式上线",
    content: "产品正式版本上线，开始运营推广",
    media: [
      {
        type: "video",
        thumbnail: "https://images.unsplash.com/photo-1542831371-29b0f74f9713?q=80&w=200&auto=format&fit=crop",
        videoSrc: "https://www.w3schools.com/html/mov_bbb.mp4"
      }
    ]
  }
];

// 测试组件属性
var testProps = {
  list: JSON.stringify(testTimelineData),
  animation: 'fade'
};

// 模拟组件测试
function testTimelineComponent() {
  console.log('开始测试 Timeline 组件...');

  // 测试数据格式验证
  var isValidData = types.validateTimelineData(testTimelineData);
  console.log('数据格式验证:', isValidData ? '通过' : '失败');

  // 测试数据
  console.log('测试数据:', testTimelineData);

  // 测试属性
  console.log('测试属性:', testProps);

  // 测试数据处理
  var processedData = testTimelineData.map(function(item, index) {
    return Object.assign({}, item, {
      animated: false,
      isLast: index === testTimelineData.length - 1
    });
  });

  console.log('处理后的数据:', processedData);

  // 测试类型工具函数
  var newItem = types.createTimelineItem('2024-10-15', '新功能发布', '发布新版本功能，增加用户体验');
  console.log('创建的新项目:', newItem);

  console.log('Timeline 组件测试完成！');
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testTimelineData: testTimelineData,
    testProps: testProps,
    testTimelineComponent: testTimelineComponent
  };
} else {
  // 在浏览器环境中直接运行测试
  testTimelineComponent();
}
