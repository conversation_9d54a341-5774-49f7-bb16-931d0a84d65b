{"name": "@byted-so-design-ai-material-lynx/timeline-comp", "version": "1.0.0", "description": "时间线组件 - Lynx 版本", "license": "ISC", "maintainers": ["zhongjiajia"], "exports": {".": "./src/components/index/index", "./dist/*": "./dist/components/index/*"}, "files": ["src", "dist", "demos", "docs", "index.json", "index.ts", "index.ttml", "index.ttss"], "scripts": {"build": "exit 0", "build:demo": "lina build --demo", "build:doc": "dux-material build:doc --type ttml-lynx", "build:h5": "CUSTOM_BUILD_DSL=Lynx2web lina build -o ./dist/web", "dev:doc": "dux-material dev:doc --type ttml-lynx", "generate:demo": "npx delight-doc-packer split", "generate:doc": "npx delight-doc-packer generate", "prepublish:only": "lcb build -s src/components -o dist/components && npm run build:h5", "prepublishOnly": "npm run build:doc", "postpublish": "npm run sync:doc", "start": "lina start --demo", "start:h5": "CUSTOM_BUILD_DSL=Lynx2web lina start", "sync:doc": "dux-material sync --skip", "test": "npm run build:demo", "test:material": "npm run build:h5 && mv dist/web/materialInfo.json demos/test/material.json", "test:type": "tsc --noemit", "test:update": "npm run build:demo"}, "dependencies": {}, "devDependencies": {"@byted-lina/cli": "latest", "@byted-lina/component-build": "latest", "@byted-lina/crystal-scripts": "latest", "@byted-lina/lepus-utils": "latest", "@byted-lina/lina-config-douyin-search": "latest", "@byted-lina/lina-douyin-search-mixins": "latest", "@byted-lina/morphling-bridge": "latest", "@byted-lina/morphling-icons": "latest", "@byted-lina/morphling-types": "latest", "@byted-lina/runtime-ng": "latest", "@byted-lina/shared-configs": "latest", "@byted-lina/types": "latest", "@byted-lina/utils": "latest", "@byted-lynx/lepus-runtime": "0.10.4", "@byted-lynx/lynx-speedy": "^3.5.4", "@byted-lynx/plugin-miniapp-nodiff": "0.10.9", "@byted-lynx/plugin-tailwindcss": "^2.4.2", "@byted-lynx/speedy-config-preset-env": "^1.6.0", "@ies/aweme-color-theme": "0.1.31", "typescript": "^4.5.2"}}