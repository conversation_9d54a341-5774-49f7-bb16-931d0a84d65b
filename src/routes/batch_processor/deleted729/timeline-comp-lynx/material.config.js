module.exports = {
  port: 5000,
  title: 'timeline-comp',
  type: 1,
  category: 3,
  group: 67,
  docs: 'docs/index.mdx',
  appType: 'ttml-lynx',
  lynxConfig: {
    input: {
      index: './demos/index/index',
      basic: './demos/basic/index',
      advanced: './demos/advanced/index',
      test: './demos/test/index',
    },
    encode: {
      defaultDisplayLinear: false,
    },
    // 从生成的 .lina/speedy.json 中获取 define 值
    define: {
      'process.env.IS_DEV': 'true',
      'process.env.IS_ONLINE': 'false',
      'process.env.IS_TEST': 'false',
      'process.env.IS_LOCAL': 'true',
      'process.env.IS_BOE': 'false',
      'process.env.SCENE': '"douyin-search"',
      'process.env.USE_GLOBAL_EXPOSURE': 'false',
      'process.env.monitor': 'false',
      'process.env.IS_WEB': 'false',
      'process.env.IS_APP': 'true',
      'process.env.IS_APP_HUOSHAN': 'false',
      'process.env.IS_NODIFF': 'false',
      'process.env.IS_LYNX_AIR': 'false',
      'process.env.IS_MAIN_THREAD_ELEMENT': 'false',
      'process.env.CUSTOM_ENABLE_LINA_TRACKER_OPTIMIZE': 'false',
    },
  },
  dependencies: {
    '@ies/aweme-color-theme': '0.1.31',
    '@byted-lina/runtime': '2.6.16',
    '@byted-lina/shared-configs': '1.0.4',
  },
};
