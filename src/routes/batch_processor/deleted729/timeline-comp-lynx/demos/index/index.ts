import { showToast } from '@byted-lina/morphling-bridge';
import { LinaCard } from '@byted-lina/runtime';

/**
 * Timeline 组件演示页面
 * 展示 timeline-comp-lynx 组件的各种使用方式和功能
 */
LinaCard({
  data: {
    // 基础示例数据
    basicTimelineData: JSON.stringify([
      {
        date: '2024-01-01',
        title: '项目启动',
        content: '开始新项目开发，制定项目计划和技术方案',
      },
      {
        date: '2024-03-15',
        title: '原型完成',
        content: '完成产品原型设计，确定用户界面和交互流程',
      },
      {
        date: '2024-06-20',
        title: '测试发布',
        content: '发布测试版本，收集用户反馈和bug报告',
      },
    ]),

    // 高级示例数据（包含媒体）
    advancedTimelineData: JSON.stringify([
      {
        date: '2024-01-01',
        title: '项目启动',
        content: '正式启动新产品开发项目，组建团队',
      },
      {
        date: '2024-03-15',
        title: '原型完成',
        content: '完成产品原型设计，获得用户好评',
        media: [
          {
            type: 'image',
            thumbnail:
              'https://images.unsplash.com/photo-1618477388954-7852f32655ec?q=80&w=200&auto=format&fit=crop',
          },
        ],
      },
      {
        date: '2024-06-20',
        title: 'Beta发布',
        content: '发布Beta测试版本，用户数突破1万',
        media: [
          {
            type: 'image',
            thumbnail:
              'https://images.unsplash.com/photo-1542831371-29b0f74f9713?q=80&w=200&auto=format&fit=crop',
          },
          {
            type: 'video',
            thumbnail:
              'https://images.unsplash.com/photo-1618477388954-7852f32655ec?q=80&w=200&auto=format&fit=crop',
            videoSrc: 'https://www.w3schools.com/html/mov_bbb.mp4',
          },
        ],
      },
      {
        date: '2024-08-30',
        title: '正式上线',
        content: '产品正式版本上线，开始运营推广',
      },
    ]),

    // 当前显示的数据集
    currentDataSet: 'basic',

    // 组件配置
    animationType: 'fade',
  },

  onReady: function () {
    console.log('Timeline 演示卡片渲染完成');
  },

  // 切换数据集
  switchDataSet: function (event) {
    var dataSet = event.currentTarget.dataset.type;
    this.setData({
      currentDataSet: dataSet,
    });

    console.log('切换到数据集:', dataSet);

    showToast({
      message: '已切换到' + (dataSet === 'basic' ? '基础' : '高级') + '数据',
      type: 'success',
    });
  },

  // 切换动画类型
  changeAnimationType: function (event) {
    var animationType = event.currentTarget.dataset.type;
    this.setData({
      animationType: animationType,
    });

    console.log('动画类型更改为:', animationType);

    showToast({
      message: '动画类型已更改为' + animationType,
      type: 'success',
    });
  },

  // 处理时间线项目点击事件
  onTimelineItemTap: function (event) {
    var item = event.detail.item;
    console.log('点击了时间线项目:', item);

    // 可以在这里添加自定义的处理逻辑
    showToast({
      message: '点击了: ' + item.title,
      type: 'info',
    });
  },

  // 重置演示
  resetDemo: function () {
    this.setData({
      currentDataSet: 'basic',
      animationType: 'fade',
    });

    showToast({
      message: '演示已重置',
      type: 'success',
    });
  },
});
