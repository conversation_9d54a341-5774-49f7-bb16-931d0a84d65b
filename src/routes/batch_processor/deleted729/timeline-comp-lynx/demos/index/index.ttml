<scroll-view class="demo-page-container" scroll-y="true">
  <view class="demo-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">Timeline 组件演示</text>
      <text class="page-subtitle">展示时间轴组件的各种功能和配置</text>
    </view>

    <!-- 控制面板 -->
    <view class="control-panel">
      <text class="panel-title">演示控制</text>

      <!-- 数据集切换 -->
      <view class="control-group">
        <text class="control-label">数据集:</text>
        <view class="button-group">
          <view
            class="control-button {{currentDataSet === 'basic' ? 'active' : ''}}"
            bindtap="switchDataSet"
            data-type="basic"
          >
            <text class="{{currentDataSet === 'basic' ? 'button-text-active' : 'button-text'}}">基础数据</text>
          </view>
          <view
            class="control-button {{currentDataSet === 'advanced' ? 'active' : ''}}"
            bindtap="switchDataSet"
            data-type="advanced"
          >
            <text class="{{currentDataSet === 'advanced' ? 'button-text-active' : 'button-text'}}">高级数据</text>
          </view>
        </view>
      </view>

      <!-- 动画类型控制 -->
      <view class="control-group">
        <text class="control-label">动画类型:</text>
        <view class="button-group">
          <view
            class="control-button {{animationType === 'fade' ? 'active' : ''}}"
            bindtap="changeAnimationType"
            data-type="fade"
          >
            <text class="{{animationType === 'fade' ? 'button-text-active' : 'button-text'}}">渐入</text>
          </view>
          <view
            class="control-button {{animationType === 'slide' ? 'active' : ''}}"
            bindtap="changeAnimationType"
            data-type="slide"
          >
            <text class="{{animationType === 'slide' ? 'button-text-active' : 'button-text'}}">滑入</text>
          </view>
          <view
            class="control-button {{animationType === 'bounce' ? 'active' : ''}}"
            bindtap="changeAnimationType"
            data-type="bounce"
          >
            <text class="{{animationType === 'bounce' ? 'button-text-active' : 'button-text'}}">弹跳</text>
          </view>
        </view>
      </view>

      <!-- 重置按钮 -->
      <view class="control-group">
        <view class="control-button reset-button" bindtap="resetDemo">
          <text class="button-text-reset">重置演示</text>
        </view>
      </view>
    </view>

    <!-- Timeline 组件演示区域 -->
    <view class="timeline-demo-section">
      <text class="section-title">组件演示</text>

      <!-- 使用 timeline 组件 -->
      <timeline-comp
        list="{{currentDataSet === 'basic' ? basicTimelineData : advancedTimelineData}}"
        animation="{{animationType}}"
        binditemtap="onTimelineItemTap"
      />
    </view>

    <!-- 代码示例 -->
    <view class="code-example-section">
      <text class="section-title">使用示例</text>
      <view class="code-block">
        <text class="code-text">timeline-comp 组件使用示例</text>
      </view>
    </view>

    <!-- 数据格式说明 -->
    <view class="data-format-section">
      <text class="section-title">数据格式</text>
      <view class="format-block">
        <text class="format-text">数据格式说明：包含 id、date、title、description、isLast 等字段</text>
      </view>
    </view>
  </view>
</scroll-view>
