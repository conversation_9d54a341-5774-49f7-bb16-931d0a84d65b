# 🔍 Lina 依赖分析和全局方法修复指南

## 🚨 问题根因分析

### 为什么 `lynx.showModal` 和 `tt.showModal` 会出错？

1. **全局对象未注入**: 在 Lina 环境中，`lynx` 和 `tt` 全局对象不会自动注入
2. **依赖导入缺失**: 需要通过 `@byted-lina/morphling-bridge` 显式导入这些方法
3. **组件定义方式错误**: 应该使用 `LinaComponent` 而不是原生的 `Component`

## 📦 Lina 依赖体系详解

### 1. **@byted-lina/morphling-bridge** 
**核心作用**: Lynx 环境的 JSBridge 桥接层

**提供的主要方法**:
```typescript
import { 
  showToast,      // 显示提示消息
  showModal,      // 显示模态框 (替代 lynx.showModal)
  searchOpen,     // 打开链接/Schema
  request,        // 网络请求
  login,          // 登录相关
  getAppInfo,     // 获取应用信息
} from '@byted-lina/morphling-bridge';
```

**正确使用方式**:
```typescript
// ❌ 错误 - 全局方法不存在
lynx.showModal({ title: '提示', content: '内容' });

// ✅ 正确 - 导入后使用
import { showModal } from '@byted-lina/morphling-bridge';
showModal({ 
  title: '提示', 
  content: '内容',
  success: (res) => {
    if (res.confirm) {
      console.log('用户确认');
    }
  }
});
```

### 2. **@byted-lina/runtime** / **@byted-lina/runtime-ng**
**核心作用**: 提供 Lina 组件和页面的定义方法

**主要导出**:
```typescript
// 基础版本
import { LinaCard, LinaComponent } from '@byted-lina/runtime';

// 新版本 (推荐)
import { LinaCard, LinaComponent } from '@byted-lina/runtime-ng';
```

**组件定义对比**:
```typescript
// ❌ 错误 - 原生 Component
Component<Data, Props, Methods>({
  // ...
});

// ✅ 正确 - Lina 组件
import { LinaComponent } from '@byted-lina/runtime-ng';
LinaComponent<Data, Props, Methods>({
  // ...
});
```

### 3. **@byted-lina/lina-douyin-search-mixins**
**核心作用**: 提供搜索场景的混入功能

**主要混入**:
```typescript
import {
  linaMixinTrackerCard,        // 埋点追踪
  linaMixinFontScaleCard,      // 字体缩放
  linaMixinHybridMonitorCard,  // 混合监控
  linaMixinPreserveDataCard,   // 数据保持
} from '@byted-lina/lina-douyin-search-mixins';

LinaCard({
  mixins: [
    linaMixinTrackerCard,
    linaMixinFontScaleCard,
    linaMixinHybridMonitorCard,
    linaMixinPreserveDataCard,
  ],
  // ...
});
```

### 4. **@byted-lina/utils**
**核心作用**: 提供通用工具函数

**常用工具**:
```typescript
import { 
  debounce,              // 防抖
  concatParamsToSchema,  // Schema 参数拼接
  formatVersion,         // 版本格式化
} from '@byted-lina/utils';
```

## ✅ Timeline 组件修复方案

### 1. **组件文件修复** (`src/components/index/index.ts`)

```typescript
// ✅ 正确的导入
import { showToast, showModal } from '@byted-lina/morphling-bridge';
import { LinaComponent } from '@byted-lina/runtime-ng';
import { Data, Props, Methods } from './types';

// ✅ 正确的组件定义
LinaComponent<Data, Props, Methods>({
  data: {
    internalTimelineData: [],
    isAnimating: false,
  },
  
  properties: {
    list: { type: String, value: '[]', observer: 'onListChange' },
    animation: { type: String, value: 'fade' }
  },

  methods: {
    onTimelineItemTap: function(event) {
      var item = this.data.internalTimelineData[index];
      
      // ✅ 使用导入的方法
      showModal({
        title: item.title || '详情',
        content: item.content || '暂无详情',
        confirmText: '确定',
        success: function(res) {
          if (res.confirm) {
            console.log('用户点击确定');
          }
        }
      });
    }
  }
});
```

### 2. **演示页面修复** (`demos/index/index.ts`)

```typescript
// ✅ 正确的导入
import { showToast } from '@byted-lina/morphling-bridge';
import { LinaCard } from '@byted-lina/runtime';

LinaCard({
  data: { /* ... */ },
  
  methods: {
    switchDataSet: function(event) {
      // ✅ 使用导入的方法
      showToast({
        message: '已切换数据集',
        type: 'success',
      });
    }
  }
});
```

### 3. **依赖配置修复** (`package.json`)

```json
{
  "dependencies": {},
  "devDependencies": {
    "@byted-lina/morphling-bridge": "latest",
    "@byted-lina/runtime-ng": "latest",
    "@byted-lina/lina-douyin-search-mixins": "latest",
    "@byted-lina/utils": "latest",
    "@byted-lina/shared-configs": "latest",
    "@byted-lina/types": "latest"
  }
}
```

## 🎯 关键修复点总结

### 1. **全局方法替换**
- `lynx.showModal` → `import { showModal } from '@byted-lina/morphling-bridge'`
- `lynx.showToast` → `import { showToast } from '@byted-lina/morphling-bridge'`
- `tt.showModal` → `import { showModal } from '@byted-lina/morphling-bridge'`

### 2. **组件定义升级**
- `Component({})` → `LinaComponent<Data, Props, Methods>({})`
- `Card({})` → `LinaCard({})`

### 3. **API 参数调整**
```typescript
// showToast 参数调整
// ❌ 旧方式
lynx.showToast({ title: '消息', duration: 1500 });

// ✅ 新方式  
showToast({ message: '消息', type: 'success' });
```

## 🚀 验证修复效果

修复后，组件应该能够：
1. ✅ 正常显示模态框
2. ✅ 正常显示提示消息
3. ✅ 在 Lina 环境中正常运行
4. ✅ 通过语法检查和类型检查

## 📚 参考资源

- **Lina 官方文档**: 组件开发指南
- **Morphling Bridge**: JSBridge 方法文档
- **Search Toolkit**: 组件最佳实践示例

修复完成后，timeline 组件将完全符合 Lina 框架规范，可以在生产环境中稳定运行。
